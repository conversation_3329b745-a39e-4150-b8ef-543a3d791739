[{"C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\Layout\\Footer.tsx": "1", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\Layout\\Header.tsx": "2", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\Layout\\index.tsx": "3", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\About.tsx": "4", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Contact.tsx": "5", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Features.tsx": "6", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Hero.tsx": "7", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\HowItWorks.tsx": "8", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Newsletter.tsx": "9", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Pricing.tsx": "10", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Testimonials.tsx": "11", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\pages\\index.tsx": "12", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\pages\\_app.tsx": "13", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\pages\\_document.tsx": "14", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\ErrorBoundary.tsx": "15", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\ui\\GlassButton.tsx": "16", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\ui\\GlassCard.tsx": "17", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\utils\\errorFilter.ts": "18", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\ui\\SyrianFlag.tsx": "19", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\ThemeController\\index.tsx": "20", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\themes\\gold-theme.ts": "21", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\themes\\index.tsx": "22", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\themes\\purple-theme.ts": "23", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\themes\\theme-utils.ts": "24", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\themes\\types.ts": "25"}, {"size": 18306, "mtime": 1749651032731, "results": "26", "hashOfConfig": "27"}, {"size": 23554, "mtime": 1749651222433, "results": "28", "hashOfConfig": "27"}, {"size": 385, "mtime": 1749589496137, "results": "29", "hashOfConfig": "27"}, {"size": 21727, "mtime": 1749651237545, "results": "30", "hashOfConfig": "27"}, {"size": 24987, "mtime": 1749651256060, "results": "31", "hashOfConfig": "27"}, {"size": 13653, "mtime": 1749651282404, "results": "32", "hashOfConfig": "27"}, {"size": 15438, "mtime": 1749651302941, "results": "33", "hashOfConfig": "27"}, {"size": 16374, "mtime": 1749651324836, "results": "34", "hashOfConfig": "27"}, {"size": 15978, "mtime": 1749600583678, "results": "35", "hashOfConfig": "27"}, {"size": 20302, "mtime": 1749651343850, "results": "36", "hashOfConfig": "27"}, {"size": 14612, "mtime": 1749607928375, "results": "37", "hashOfConfig": "27"}, {"size": 3319, "mtime": 1749593558895, "results": "38", "hashOfConfig": "27"}, {"size": 2845, "mtime": 1749647582298, "results": "39", "hashOfConfig": "27"}, {"size": 1646, "mtime": 1749589446276, "results": "40", "hashOfConfig": "27"}, {"size": 2677, "mtime": 1749597314087, "results": "41", "hashOfConfig": "27"}, {"size": 2428, "mtime": 1749596227387, "results": "42", "hashOfConfig": "27"}, {"size": 989, "mtime": 1749596212722, "results": "43", "hashOfConfig": "27"}, {"size": 2120, "mtime": 1749597019188, "results": "44", "hashOfConfig": "27"}, {"size": 2328, "mtime": 1749607729735, "results": "45", "hashOfConfig": "27"}, {"size": 9033, "mtime": 1749651379121, "results": "46", "hashOfConfig": "27"}, {"size": 5749, "mtime": 1749647330487, "results": "47", "hashOfConfig": "27"}, {"size": 4060, "mtime": 1749651461056, "results": "48", "hashOfConfig": "27"}, {"size": 5846, "mtime": 1749647373011, "results": "49", "hashOfConfig": "27"}, {"size": 5926, "mtime": 1749647406588, "results": "50", "hashOfConfig": "27"}, {"size": 3583, "mtime": 1749647289790, "results": "51", "hashOfConfig": "27"}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "ody2rz", {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\Layout\\Footer.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\Layout\\Header.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\Layout\\index.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\About.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Contact.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Features.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Hero.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\HowItWorks.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Newsletter.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Pricing.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\sections\\Testimonials.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\pages\\index.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\pages\\_app.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\pages\\_document.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\ErrorBoundary.tsx", [], ["127"], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\ui\\GlassButton.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\ui\\GlassCard.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\utils\\errorFilter.ts", ["128", "129", "130", "131", "132", "133", "134", "135"], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\ui\\SyrianFlag.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\components\\ThemeController\\index.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\themes\\gold-theme.ts", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\themes\\index.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\themes\\purple-theme.ts", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\themes\\theme-utils.ts", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\landing-page\\src\\themes\\types.ts", [], [], {"ruleId": "136", "severity": 1, "message": "137", "line": 37, "column": 7, "nodeType": "138", "messageId": "139", "endLine": 37, "endColumn": 20, "suggestions": "140", "suppressions": "141"}, {"ruleId": "136", "severity": 1, "message": "137", "line": 7, "column": 30, "nodeType": "138", "messageId": "139", "endLine": 7, "endColumn": 43}, {"ruleId": "136", "severity": 1, "message": "137", "line": 8, "column": 29, "nodeType": "138", "messageId": "139", "endLine": 8, "endColumn": 41}, {"ruleId": "136", "severity": 1, "message": "137", "line": 37, "column": 5, "nodeType": "138", "messageId": "139", "endLine": 37, "endColumn": 18}, {"ruleId": "142", "severity": 1, "message": "143", "line": 37, "column": 31, "nodeType": "144", "messageId": "145", "endLine": 37, "endColumn": 34, "suggestions": "146"}, {"ruleId": "136", "severity": 1, "message": "137", "line": 44, "column": 5, "nodeType": "138", "messageId": "139", "endLine": 44, "endColumn": 17}, {"ruleId": "142", "severity": 1, "message": "143", "line": 44, "column": 30, "nodeType": "144", "messageId": "145", "endLine": 44, "endColumn": 33, "suggestions": "147"}, {"ruleId": "136", "severity": 1, "message": "137", "line": 73, "column": 3, "nodeType": "138", "messageId": "139", "endLine": 73, "endColumn": 16}, {"ruleId": "136", "severity": 1, "message": "137", "line": 74, "column": 3, "nodeType": "138", "messageId": "139", "endLine": 74, "endColumn": 15}, "no-console", "Unexpected console statement.", "MemberExpression", "unexpected", ["148"], ["149"], "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["150", "151"], ["152", "153"], {"messageId": "154", "data": "155", "fix": "156", "desc": "157"}, {"kind": "158", "justification": "159"}, {"messageId": "160", "fix": "161", "desc": "162"}, {"messageId": "163", "fix": "164", "desc": "165"}, {"messageId": "160", "fix": "166", "desc": "162"}, {"messageId": "163", "fix": "167", "desc": "165"}, "removeConsole", {"propertyName": "168"}, {"range": "169", "text": "159"}, "Remove the console.error().", "directive", "", "suggestUnknown", {"range": "170", "text": "171"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "172", "text": "173"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "174", "text": "171"}, {"range": "175", "text": "173"}, "error", [1086, 1152], [1100, 1103], "unknown", [1100, 1103], "never", [1286, 1289], [1286, 1289]]