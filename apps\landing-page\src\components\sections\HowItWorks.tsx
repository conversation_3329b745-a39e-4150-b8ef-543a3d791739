import { useState, useEffect } from 'react';
import { useTranslation } from 'next-i18next';
import { useRouter } from 'next/router';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { useTheme } from '@/themes';
import {
  DocumentPlusIcon,
  InboxArrowDownIcon,
  UserIcon,
  PlayIcon,
  UserPlusIcon,
  MagnifyingGlassIcon,
  PaperAirplaneIcon,
  BanknotesIcon,
} from '@heroicons/react/24/outline';

export default function HowItWorks() {
  const { t } = useTranslation('landing');
  const router = useRouter();
  const { locale } = router;
  const isRTL = locale === 'ar';
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const { currentTheme } = useTheme();

  const [activeTab, setActiveTab] = useState<'clients' | 'experts'>('clients');

  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  // Track mouse movement for interactive effects
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({
        x: (e.clientX / window.innerWidth) * 100,
        y: (e.clientY / window.innerHeight) * 100,
      });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  // Steps for clients
  const clientSteps = [
    {
      title: t('howItWorks.forClients.steps.0.title'),
      description: t('howItWorks.forClients.steps.0.description'),
      icon: DocumentPlusIcon,
    },
    {
      title: t('howItWorks.forClients.steps.1.title'),
      description: t('howItWorks.forClients.steps.1.description'),
      icon: InboxArrowDownIcon,
    },
    {
      title: t('howItWorks.forClients.steps.2.title'),
      description: t('howItWorks.forClients.steps.2.description'),
      icon: UserIcon,
    },
    {
      title: t('howItWorks.forClients.steps.3.title'),
      description: t('howItWorks.forClients.steps.3.description'),
      icon: PlayIcon,
    },
  ];

  // Steps for experts
  const expertSteps = [
    {
      title: t('howItWorks.forExperts.steps.0.title'),
      description: t('howItWorks.forExperts.steps.0.description'),
      icon: UserPlusIcon,
    },
    {
      title: t('howItWorks.forExperts.steps.1.title'),
      description: t('howItWorks.forExperts.steps.1.description'),
      icon: MagnifyingGlassIcon,
    },
    {
      title: t('howItWorks.forExperts.steps.2.title'),
      description: t('howItWorks.forExperts.steps.2.description'),
      icon: PaperAirplaneIcon,
    },
    {
      title: t('howItWorks.forExperts.steps.3.title'),
      description: t('howItWorks.forExperts.steps.3.description'),
      icon: BanknotesIcon,
    },
  ];

  const currentSteps = activeTab === 'clients' ? clientSteps : expertSteps;

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: 'easeOut',
      },
    },
  };

  return (
    <section
      id="how-it-works"
      ref={ref}
      className="relative section-padding overflow-hidden"
      style={{
        background: `
          radial-gradient(circle at ${mousePosition.x}% ${mousePosition.y}%, ${currentTheme.colors.primary[500]}20 0%, transparent 60%),
          ${currentTheme.backgrounds.secondary}
        `
      }}
    >
      {/* Enhanced Background with Glass Orbs */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* Theme-Aware Glass Orbs */}
        <motion.div
          animate={{
            y: [-22, 22, -22],
            rotate: [0, 180, 360],
            scale: [1, 1.1, 1]
          }}
          transition={{ duration: 19, repeat: Infinity, ease: 'easeInOut' }}
          className="absolute top-24 right-16 w-30 h-30 rounded-full opacity-12"
          style={{
            background: currentTheme.colors.glass.background,
            backdropFilter: currentTheme.colors.glass.backdropBlur,
            WebkitBackdropFilter: currentTheme.colors.glass.backdropBlur,
            border: `1px solid ${currentTheme.colors.glass.border}`,
            boxShadow: currentTheme.colors.glass.shadow
          }}
        />

        <motion.div
          animate={{
            y: [28, -28, 28],
            rotate: [360, 180, 0],
            scale: [1.1, 1, 1.1]
          }}
          transition={{ duration: 21, repeat: Infinity, ease: 'easeInOut' }}
          className="absolute bottom-36 left-14 w-26 h-26 rounded-full opacity-10"
          style={{
            background: `${currentTheme.colors.secondary[500]}20`,
            backdropFilter: 'blur(18px)',
            WebkitBackdropFilter: 'blur(18px)',
            border: `1px solid ${currentTheme.colors.secondary[500]}40`,
            boxShadow: `0 8px 32px ${currentTheme.colors.secondary[500]}20`
          }}
        />

        {/* Floating Particles */}
        {[...Array(6)].map((_, i) => (
          <motion.div
            key={i}
            animate={{
              y: [-12, 12, -12],
              x: [-6, 6, -6],
              opacity: [0.1, 0.3, 0.1],
              scale: [1, 1.2, 1]
            }}
            transition={{
              duration: 5 + i * 1.2,
              repeat: Infinity,
              ease: 'easeInOut',
              delay: i * 0.8
            }}
            className="absolute w-1 h-1 bg-white rounded-full"
            style={{
              left: `${20 + (i * 12)}%`,
              top: `${30 + (i * 10)}%`,
              filter: 'blur(0.5px)'
            }}
          />
        ))}
      </div>
      <div className="container mx-auto container-padding">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={inView ? 'visible' : 'hidden'}
          className="text-center mb-16"
        >
          {/* Enhanced Section Header with Premium Typography */}
          <motion.div
            variants={itemVariants}
            className="relative mb-8"
          >


            <motion.h2
              variants={itemVariants}
              className={`heading-lg mb-4 text-arabic-premium ${
                isRTL
                  ? 'font-cairo text-4xl lg:text-5xl xl:text-6xl font-bold tracking-tight'
                  : 'font-display text-4xl lg:text-5xl xl:text-6xl font-bold tracking-tight'
              }`}
              style={{
                background: currentTheme.gradients.text,
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                textShadow: '0 4px 8px rgba(0, 0, 0, 0.3)',
                filter: 'drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2))'
              }}
            >
              {t('howItWorks.title')}
            </motion.h2>
          </motion.div>

          <motion.p
            variants={itemVariants}
            className={`text-xl lg:text-2xl max-w-4xl mx-auto leading-relaxed ${
              isRTL ? 'font-tajawal' : 'font-sans'
            }`}
            style={{
              color: currentTheme.colors.text.secondary,
              textShadow: '0 2px 4px rgba(0, 0, 0, 0.2)'
            }}
          >
            {t('howItWorks.subtitle')}
          </motion.p>
        </motion.div>

        {/* Enhanced Tab Navigation */}
        <motion.div
          variants={itemVariants}
          initial="hidden"
          animate={inView ? 'visible' : 'hidden'}
          className="flex justify-center mb-12"
        >
          <div
            className="relative p-2 rounded-2xl overflow-hidden"
            style={{
              background: currentTheme.colors.glass.background,
              backdropFilter: currentTheme.colors.glass.backdropBlur,
              WebkitBackdropFilter: currentTheme.colors.glass.backdropBlur,
              border: `1px solid ${currentTheme.colors.glass.border}`,
              boxShadow: currentTheme.colors.glass.shadow
            }}
          >
            {/* Theme Accent */}
            <div className="absolute top-1 right-1 flex space-x-1 rtl:space-x-reverse opacity-30">
              <div
                className="w-1 h-4 rounded-full"
                style={{ background: currentTheme.colors.primary[500] }}
              ></div>
              <div
                className="w-1 h-4 rounded-full"
                style={{ background: currentTheme.colors.primary[600] }}
              ></div>
            </div>

            <motion.button
              type="button"
              onClick={() => setActiveTab('clients')}
              className={`relative px-8 py-4 rounded-xl font-semibold transition-all duration-300 ${
                isRTL ? 'font-cairo' : 'font-sans'
              } ${
                activeTab === 'clients'
                  ? 'text-white shadow-lg'
                  : 'hover:text-white'
              }`}
              style={{
                background: activeTab === 'clients'
                  ? currentTheme.gradients.primary
                  : 'transparent',
                color: activeTab === 'clients'
                  ? 'white'
                  : currentTheme.colors.text.secondary,
                boxShadow: activeTab === 'clients'
                  ? currentTheme.shadows.md
                  : 'none'
              }}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              {t('howItWorks.forClients.title')}
            </motion.button>
            <motion.button
              type="button"
              onClick={() => setActiveTab('experts')}
              className={`relative px-8 py-4 rounded-xl font-semibold transition-all duration-300 ${
                isRTL ? 'font-cairo' : 'font-sans'
              } ${
                activeTab === 'experts'
                  ? 'text-white shadow-lg'
                  : 'hover:text-white'
              }`}
              style={{
                background: activeTab === 'experts'
                  ? currentTheme.gradients.primary
                  : 'transparent',
                color: activeTab === 'experts'
                  ? 'white'
                  : currentTheme.colors.text.secondary,
                boxShadow: activeTab === 'experts'
                  ? currentTheme.shadows.md
                  : 'none'
              }}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              {t('howItWorks.forExperts.title')}
            </motion.button>
          </div>
        </motion.div>

        {/* Steps */}
        <motion.div
          key={activeTab}
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
        >
          {currentSteps.map((step, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              className="relative"
            >

              
              <div
                className="relative p-8 text-center h-full rounded-2xl overflow-hidden group"
                style={{
                  background: currentTheme.gradients.card,
                  backdropFilter: currentTheme.colors.glass.backdropBlur,
                  WebkitBackdropFilter: currentTheme.colors.glass.backdropBlur,
                  border: `1px solid ${currentTheme.colors.glass.border}`,
                  boxShadow: currentTheme.colors.glass.shadow,
                  transition: 'all 0.3s ease-in-out'
                }}
              >
                {/* Enhanced Step Number with Theme Colors */}
                <motion.div
                  className="relative w-20 h-20 mx-auto mb-6 rounded-full flex items-center justify-center text-white font-bold text-2xl overflow-hidden"
                  style={{
                    background: index % 2 === 0
                      ? currentTheme.gradients.primary
                      : currentTheme.gradients.secondary,
                    boxShadow: currentTheme.shadows.lg
                  }}
                  whileHover={{ scale: 1.1, rotate: 5 }}
                  transition={{ duration: 0.3 }}
                >
                  <span className={`relative z-10 ${isRTL ? 'font-cairo' : 'font-display'}`}>
                    {index + 1}
                  </span>
                  {/* Shimmer effect */}
                  <div
                    className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                    style={{
                      background: 'linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%)',
                      animation: 'glassShimmer 1.5s ease-in-out infinite'
                    }}
                  />
                </motion.div>

                {/* Enhanced Icon */}
                <motion.div
                  className="w-16 h-16 mx-auto mb-6 rounded-2xl flex items-center justify-center relative overflow-hidden"
                  style={{
                    background: currentTheme.colors.glass.background,
                    backdropFilter: 'blur(10px)',
                    border: `1px solid ${currentTheme.colors.glass.border}`
                  }}
                  whileHover={{ scale: 1.05, y: -2 }}
                  transition={{ duration: 0.2 }}
                >
                  <step.icon
                    className="w-8 h-8 relative z-10"
                    style={{ color: currentTheme.colors.text.primary }}
                  />
                </motion.div>

                {/* Enhanced Content */}
                <motion.h3
                  className={`heading-sm mb-4 font-bold ${
                    isRTL ? 'font-cairo text-xl lg:text-2xl' : 'font-display text-xl lg:text-2xl'
                  }`}
                  style={{
                    color: currentTheme.colors.text.primary,
                    textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)'
                  }}
                >
                  {step.title}
                </motion.h3>
                <p
                  className={`leading-relaxed ${
                    isRTL ? 'font-tajawal' : 'font-sans'
                  }`}
                  style={{ color: currentTheme.colors.text.secondary }}
                >
                  {step.description}
                </p>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Enhanced CTA */}
        <motion.div
          variants={itemVariants}
          initial="hidden"
          animate={inView ? 'visible' : 'hidden'}
          className="text-center mt-20"
        >
          <motion.button
            type="button"
            className={`relative px-12 py-5 text-xl font-semibold rounded-2xl overflow-hidden group ${
              isRTL ? 'font-cairo' : 'font-display'
            }`}
            style={{
              background: currentTheme.gradients.button,
              backdropFilter: 'blur(20px)',
              border: `1px solid ${currentTheme.colors.glass.border}`,
              boxShadow: currentTheme.shadows.premium,
              color: 'white',
              textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)'
            }}
            whileHover={{
              scale: 1.05,
              y: -3,
              transition: { duration: 0.2 }
            }}
            whileTap={{ scale: 0.95 }}
          >
            {/* Button shimmer effect */}
            <div
              className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
              style={{
                background: 'linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%)',
                animation: 'glassShimmer 1.5s ease-in-out infinite'
              }}
            />
            <span className="relative z-10">
              {activeTab === 'clients'
                ? (isRTL ? 'انشر مشروعك الآن' : 'Post Your Project Now')
                : (isRTL ? 'انضم كخبير' : 'Join as Expert')
              }
            </span>
          </motion.button>
        </motion.div>
      </div>
    </section>
  );
}
