import { useTranslation } from 'next-i18next';
import { useRouter } from 'next/router';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { useState, useEffect } from 'react';
import {
  HeartIcon,
  StarIcon,
  LightBulbIcon,
  UserGroupIcon,
} from '@heroicons/react/24/outline';
import SyrianFlag from '../ui/SyrianFlag';
import { useTheme } from '@/themes';

export default function About() {
  const { t } = useTranslation('landing');
  const router = useRouter();
  const { locale } = router;
  const isRTL = locale === 'ar';
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const { currentTheme } = useTheme();

  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  // Track mouse movement for interactive effects
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({
        x: (e.clientX / window.innerWidth) * 100,
        y: (e.clientY / window.innerHeight) * 100,
      });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  // Icon mapping for values
  const iconMap = {
    trust: HeartIcon,
    quality: StarIcon,
    innovation: LightBulbIcon,
    community: UserGroupIcon,
  };

  // Get values from translation
  const values = Array.from({ length: 4 }, (_, i) => {
    const title = t(`about.values.${i}.title`);
    const description = t(`about.values.${i}.description`);
    
    // Map Arabic titles to icon keys
    const iconKey = title === 'الثقة' ? 'trust' :
                   title === 'الجودة' ? 'quality' :
                   title === 'الابتكار' ? 'innovation' :
                   title === 'المجتمع' ? 'community' :
                   title === 'Trust' ? 'trust' :
                   title === 'Quality' ? 'quality' :
                   title === 'Innovation' ? 'innovation' :
                   title === 'Community' ? 'community' : 'trust';
    
    return {
      title,
      description,
      icon: iconKey as keyof typeof iconMap,
    };
  });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: 'easeOut',
      },
    },
  };

  return (
    <section
      id="about"
      ref={ref}
      className="relative section-padding overflow-hidden"
      style={{
        background: `
          radial-gradient(circle at ${mousePosition.x}% ${mousePosition.y}%, ${currentTheme.colors.secondary[500]}20 0%, transparent 60%),
          ${currentTheme.backgrounds.primary}
        `
      }}
    >
      {/* Enhanced Background with Glass Orbs */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* Theme-Aware Glass Orbs */}
        <motion.div
          animate={{
            y: [-25, 25, -25],
            rotate: [0, 180, 360],
            scale: [1, 1.1, 1]
          }}
          transition={{ duration: 20, repeat: Infinity, ease: 'easeInOut' }}
          className="absolute top-32 left-12 w-32 h-32 rounded-full opacity-12"
          style={{
            background: currentTheme.colors.glass.background,
            backdropFilter: currentTheme.colors.glass.backdropBlur,
            WebkitBackdropFilter: currentTheme.colors.glass.backdropBlur,
            border: `1px solid ${currentTheme.colors.glass.border}`,
            boxShadow: currentTheme.colors.glass.shadow
          }}
        />

        <motion.div
          animate={{
            y: [35, -35, 35],
            rotate: [360, 180, 0],
            scale: [1.1, 1, 1.1]
          }}
          transition={{ duration: 24, repeat: Infinity, ease: 'easeInOut' }}
          className="absolute bottom-40 right-20 w-28 h-28 rounded-full opacity-15"
          style={{
            background: `${currentTheme.colors.accent[500]}20`,
            backdropFilter: 'blur(20px)',
            WebkitBackdropFilter: 'blur(20px)',
            border: `1px solid ${currentTheme.colors.accent[500]}40`,
            boxShadow: `0 8px 32px ${currentTheme.colors.accent[500]}20`
          }}
        />

        {/* Floating Particles */}
        {[...Array(10)].map((_, i) => (
          <motion.div
            key={i}
            animate={{
              y: [-18, 18, -18],
              x: [-9, 9, -9],
              opacity: [0.1, 0.3, 0.1],
              scale: [1, 1.2, 1]
            }}
            transition={{
              duration: 7 + i * 1.8,
              repeat: Infinity,
              ease: 'easeInOut',
              delay: i * 0.6
            }}
            className="absolute w-1.5 h-1.5 bg-white rounded-full"
            style={{
              left: `${12 + (i * 9)}%`,
              top: `${20 + (i * 7)}%`,
              filter: 'blur(0.5px)'
            }}
          />
        ))}
      </div>
      <div className="container mx-auto container-padding relative z-10">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={inView ? 'visible' : 'hidden'}
          className="max-w-6xl mx-auto"
        >
          {/* Enhanced Header with Glass Effect */}
          <div className="text-center mb-20">
            <motion.div
              variants={itemVariants}
              className="relative mb-8"
            >
              <motion.h2
                className={`heading-lg mb-6 relative z-10 px-8 py-4 text-arabic-premium ${
                  isRTL ? 'font-cairo' : 'font-display'
                }`}
                style={{
                  background: currentTheme.gradients.text,
                  backgroundClip: 'text',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  textShadow: '0 4px 8px rgba(0, 0, 0, 0.3)',
                  filter: 'drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2))'
                }}
                initial={{ opacity: 0, y: 30, filter: 'blur(10px)' }}
                animate={{ opacity: 1, y: 0, filter: 'blur(0px)' }}
                transition={{ duration: 0.8, ease: 'easeOut' }}
              >
                {t('about.title')}
              </motion.h2>

              {/* Enhanced Glass backdrop for title */}
              <div
                className="absolute inset-0 -m-4 rounded-2xl opacity-25"
                style={{
                  background: currentTheme.colors.glass.background,
                  backdropFilter: currentTheme.colors.glass.backdropBlur,
                  WebkitBackdropFilter: currentTheme.colors.glass.backdropBlur,
                  border: `1px solid ${currentTheme.colors.glass.border}`,
                  boxShadow: currentTheme.shadows.premium
                }}
              />
            </motion.div>

            <motion.p
              variants={itemVariants}
              className={`text-xl max-w-3xl mx-auto leading-relaxed text-arabic px-6 ${
                isRTL ? 'font-tajawal' : 'font-sans'
              }`}
              style={{ color: currentTheme.colors.text.secondary }}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              {t('about.subtitle')}
            </motion.p>
          </div>

          {/* Main Content */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center mb-16">
            {/* Enhanced Text Content */}
            <motion.div variants={itemVariants} className="space-y-6">
              <p
                className={`text-lg leading-relaxed ${
                  isRTL ? 'font-tajawal' : 'font-sans'
                }`}
                style={{ color: currentTheme.colors.text.secondary }}
              >
                {t('about.content')}
              </p>

              <div className="space-y-4">
                <motion.div
                  className="rounded-lg p-6 overflow-hidden relative"
                  style={{
                    background: currentTheme.gradients.card,
                    backdropFilter: currentTheme.colors.glass.backdropBlur,
                    WebkitBackdropFilter: currentTheme.colors.glass.backdropBlur,
                    border: `1px solid ${currentTheme.colors.glass.border}`,
                    boxShadow: currentTheme.shadows.md
                  }}
                  whileHover={{ scale: 1.02, y: -2 }}
                  transition={{ duration: 0.2 }}
                >
                  <h3
                    className={`font-semibold mb-2 ${
                      isRTL ? 'font-cairo' : 'font-display'
                    }`}
                    style={{ color: currentTheme.colors.text.primary }}
                  >
                    {isRTL ? 'مهمتنا' : 'Our Mission'}
                  </h3>
                  <p
                    className={isRTL ? 'font-tajawal' : 'font-sans'}
                    style={{ color: currentTheme.colors.text.secondary }}
                  >
                    {t('about.mission')}
                  </p>
                </motion.div>

                <motion.div
                  className="rounded-lg p-6 overflow-hidden relative"
                  style={{
                    background: currentTheme.gradients.card,
                    backdropFilter: currentTheme.colors.glass.backdropBlur,
                    WebkitBackdropFilter: currentTheme.colors.glass.backdropBlur,
                    border: `1px solid ${currentTheme.colors.glass.border}`,
                    boxShadow: currentTheme.shadows.md
                  }}
                  whileHover={{ scale: 1.02, y: -2 }}
                  transition={{ duration: 0.2 }}
                >
                  <h3
                    className={`font-semibold mb-2 ${
                      isRTL ? 'font-cairo' : 'font-display'
                    }`}
                    style={{ color: currentTheme.colors.text.primary }}
                  >
                    {isRTL ? 'رؤيتنا' : 'Our Vision'}
                  </h3>
                  <p
                    className={isRTL ? 'font-tajawal' : 'font-sans'}
                    style={{ color: currentTheme.colors.text.secondary }}
                  >
                    {t('about.vision')}
                  </p>
                </motion.div>
              </div>
            </motion.div>

            {/* Enhanced Visual Element */}
            <motion.div
              variants={itemVariants}
              className="relative"
            >
              <div
                className="p-8 lg:p-12 rounded-2xl overflow-hidden relative"
                style={{
                  background: currentTheme.gradients.card,
                  backdropFilter: currentTheme.colors.glass.backdropBlur,
                  WebkitBackdropFilter: currentTheme.colors.glass.backdropBlur,
                  border: `1px solid ${currentTheme.colors.glass.border}`,
                  boxShadow: currentTheme.shadows.premium
                }}
              >
                {/* Theme Accent Elements */}
                <div className="absolute top-4 right-4 flex space-x-1 rtl:space-x-reverse opacity-20">
                  <div
                    className="w-2 h-12 rounded-full"
                    style={{ background: currentTheme.colors.primary[500] }}
                  ></div>
                  <div
                    className="w-2 h-12 rounded-full"
                    style={{ background: currentTheme.colors.primary[600] }}
                  ></div>
                </div>

                {/* Accurate Syrian Flag */}
                <div className="flex items-center justify-center mb-8">
                  <motion.div
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.8, delay: 0.5 }}
                    whileHover={{ scale: 1.05 }}
                    className="relative"
                  >
                    <SyrianFlag width={160} height={107} className="shadow-2xl" />
                    {/* Theme-aware accent glow */}
                    <div className="absolute inset-0 rounded-lg opacity-30 pointer-events-none"
                         style={{
                           background: `radial-gradient(circle, ${currentTheme.colors.primary[500]}40 0%, transparent 70%)`,
                           filter: 'blur(8px)'
                         }}
                    />
                  </motion.div>
                </div>

                <div className="text-center">
                  <h3
                    className={`heading-sm mb-4 ${
                      isRTL ? 'font-cairo' : 'font-display'
                    }`}
                    style={{
                      background: currentTheme.gradients.text,
                      backgroundClip: 'text',
                      WebkitBackgroundClip: 'text',
                      WebkitTextFillColor: 'transparent',
                      textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)'
                    }}
                  >
                    {isRTL ? 'فخورون بهويتنا السورية' : 'Proud of Our Syrian Identity'}
                  </h3>
                  <p
                    className={`leading-relaxed ${
                      isRTL ? 'font-tajawal' : 'font-sans'
                    }`}
                    style={{ color: currentTheme.colors.text.secondary }}
                  >
                    {isRTL
                      ? 'نعمل على تمكين المواهب السورية وربطها بالفرص العالمية مع الحفاظ على هويتنا وقيمنا الأصيلة'
                      : 'We work to empower Syrian talents and connect them with global opportunities while preserving our authentic identity and values'
                    }
                  </p>
                </div>
              </div>
            </motion.div>
          </div>

          {/* Enhanced Values */}
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate={inView ? 'visible' : 'hidden'}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
          >
            {values.map((value, index) => {
              const IconComponent = iconMap[value.icon];

              return (
                <motion.div
                  key={index}
                  variants={itemVariants}
                  className="text-center group relative"
                  whileHover={{ y: -8 }}
                  transition={{ duration: 0.3 }}
                >
                  <motion.div
                    className="w-16 h-16 mx-auto mb-4 rounded-2xl flex items-center justify-center relative overflow-hidden"
                    style={{
                      background: currentTheme.gradients.primary,
                      boxShadow: currentTheme.shadows.lg
                    }}
                    whileHover={{ scale: 1.1, rotate: 5 }}
                    transition={{ duration: 0.3 }}
                  >
                    <IconComponent
                      className="w-8 h-8 text-white relative z-10"
                    />
                    {/* Shimmer effect */}
                    <div
                      className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                      style={{
                        background: 'linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%)',
                        animation: 'glassShimmer 1.5s ease-in-out infinite'
                      }}
                    />
                  </motion.div>

                  <h3
                    className={`heading-sm mb-3 ${
                      isRTL ? 'font-cairo' : 'font-display'
                    }`}
                    style={{ color: currentTheme.colors.text.primary }}
                  >
                    {value.title}
                  </h3>

                  <p
                    className={`text-sm leading-relaxed ${
                      isRTL ? 'font-tajawal' : 'font-sans'
                    }`}
                    style={{ color: currentTheme.colors.text.secondary }}
                  >
                    {value.description}
                  </p>
                </motion.div>
              );
            })}
          </motion.div>

          {/* Enhanced CTA */}
          <motion.div
            variants={itemVariants}
            initial="hidden"
            animate={inView ? 'visible' : 'hidden'}
            className="text-center mt-16"
          >
            <div
              className="rounded-2xl p-8 lg:p-12 relative overflow-hidden"
              style={{
                background: currentTheme.colors.glass.background,
                backdropFilter: currentTheme.colors.glass.backdropBlur,
                WebkitBackdropFilter: currentTheme.colors.glass.backdropBlur,
                border: `1px solid ${currentTheme.colors.glass.border}`,
                boxShadow: currentTheme.shadows.premium
              }}
            >
              {/* Theme Accent Elements */}
              <div className="absolute top-4 right-4 flex space-x-2 rtl:space-x-reverse opacity-20">
                <div
                  className="w-3 h-16 rounded-full"
                  style={{ background: currentTheme.colors.primary[500] }}
                ></div>
                <div
                  className="w-3 h-16 rounded-full"
                  style={{ background: currentTheme.colors.primary[600] }}
                ></div>
              </div>

              <h3
                className={`heading-md mb-4 ${
                  isRTL ? 'font-cairo' : 'font-display'
                }`}
                style={{ color: currentTheme.colors.text.primary }}
              >
                {isRTL ? 'انضم إلى رحلتنا' : 'Join Our Journey'}
              </h3>
              <p
                className={`text-lg mb-8 max-w-2xl mx-auto ${
                  isRTL ? 'font-tajawal' : 'font-sans'
                }`}
                style={{ color: currentTheme.colors.text.secondary }}
              >
                {isRTL
                  ? 'كن جزءاً من قصة نجاح فريلا سوريا وساهم في بناء مستقبل أفضل للعمل الحر في سوريا'
                  : 'Be part of Freela Syria\'s success story and contribute to building a better future for freelancing in Syria'
                }
              </p>
              <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
                <motion.button
                  type="button"
                  className={`relative text-lg px-8 py-4 rounded-xl font-semibold overflow-hidden group ${
                    isRTL ? 'font-cairo' : 'font-display'
                  }`}
                  style={{
                    background: currentTheme.gradients.button,
                    color: 'white',
                    boxShadow: currentTheme.shadows.md,
                    textShadow: '0 1px 2px rgba(0, 0, 0, 0.3)'
                  }}
                  whileHover={{ scale: 1.05, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <span className="relative z-10">
                    {isRTL ? 'ابدأ رحلتك' : 'Start Your Journey'}
                  </span>
                  {/* Button shimmer effect */}
                  <div
                    className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                    style={{
                      background: 'linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%)',
                      animation: 'glassShimmer 1.5s ease-in-out infinite'
                    }}
                  />
                </motion.button>
                <motion.button
                  type="button"
                  className={`relative text-lg px-8 py-4 rounded-xl font-semibold overflow-hidden group ${
                    isRTL ? 'font-cairo' : 'font-display'
                  }`}
                  style={{
                    background: currentTheme.colors.glass.background,
                    backdropFilter: currentTheme.colors.glass.backdropBlur,
                    border: `1px solid ${currentTheme.colors.glass.border}`,
                    color: currentTheme.colors.text.primary,
                    boxShadow: currentTheme.shadows.md
                  }}
                  whileHover={{ scale: 1.05, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <span className="relative z-10">
                    {isRTL ? 'تعرف على الفريق' : 'Meet the Team'}
                  </span>
                  {/* Button shimmer effect */}
                  <div
                    className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                    style={{
                      background: 'linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.2) 50%, transparent 100%)',
                      animation: 'glassShimmer 1.5s ease-in-out infinite'
                    }}
                  />
                </motion.button>
              </div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}
